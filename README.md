# Java数据库连接示例 - Statement vs PreparedStatement

## 文件说明

- `DatabaseExample.java` - MySQL数据库完整示例
- `SQLiteExample.java` - SQLite数据库示例（推荐学习使用）

## Statement vs PreparedStatement 对比

### 1. Statement

**特点：**
- 每次执行都需要编译SQL语句
- SQL语句是静态拼接的
- 容易受到SQL注入攻击
- 适合执行静态SQL语句

**示例：**
```java
Statement stmt = conn.createStatement();
String sql = "SELECT * FROM users WHERE name = '" + userName + "'";
ResultSet rs = stmt.executeQuery(sql);
```

**优点：**
- 简单直接
- 适合一次性执行的SQL

**缺点：**
- 性能较差（每次都要编译）
- 安全性差（SQL注入风险）
- 代码可读性差（字符串拼接）

### 2. PreparedStatement

**特点：**
- SQL语句预编译，只编译一次
- 使用参数占位符（?）
- 自动防止SQL注入
- 适合重复执行的SQL语句

**示例：**
```java
PreparedStatement pstmt = conn.prepareStatement("SELECT * FROM users WHERE name = ?");
pstmt.setString(1, userName);
ResultSet rs = pstmt.executeQuery();
```

**优点：**
- 性能好（预编译）
- 安全性高（防SQL注入）
- 代码清晰（参数分离）
- 支持批处理

**缺点：**
- 稍微复杂一些
- 不适合动态SQL结构

## 主要区别总结

| 特性 | Statement | PreparedStatement |
|------|-----------|-------------------|
| 编译方式 | 每次编译 | 预编译一次 |
| 性能 | 较差 | 较好 |
| SQL注入防护 | 无 | 有 |
| 参数设置 | 字符串拼接 | 参数占位符 |
| 批处理支持 | 有限 | 完整支持 |
| 适用场景 | 静态SQL | 动态参数SQL |

## 安全性演示

### SQL注入示例

**不安全的Statement：**
```java
String userInput = "'; DROP TABLE users; --";
String sql = "SELECT * FROM users WHERE name = '" + userInput + "'";
// 生成的SQL: SELECT * FROM users WHERE name = ''; DROP TABLE users; --'
// 可能会删除整个表！
```

**安全的PreparedStatement：**
```java
String userInput = "'; DROP TABLE users; --";
PreparedStatement pstmt = conn.prepareStatement("SELECT * FROM users WHERE name = ?");
pstmt.setString(1, userInput);
// 恶意输入会被当作普通字符串处理，不会执行恶意SQL
```

## 最佳实践

1. **优先使用PreparedStatement**
   - 除非有特殊需求，否则总是使用PreparedStatement

2. **资源管理**
   - 使用try-with-resources语句自动关闭资源
   - 或者在finally块中手动关闭

3. **批处理**
   - 大量数据操作时使用批处理提高性能

4. **参数验证**
   - 即使使用PreparedStatement，也要验证输入参数

## 运行示例

### 运行SQLite示例（推荐）

1. 下载SQLite JDBC驱动：
   ```bash
   # 下载 sqlite-jdbc-x.x.x.jar
   ```

2. 编译运行：
   ```bash
   javac -cp ".:sqlite-jdbc-x.x.x.jar" SQLiteExample.java
   java -cp ".:sqlite-jdbc-x.x.x.jar" SQLiteExample
   ```

### 运行MySQL示例

1. 安装MySQL数据库
2. 创建数据库：
   ```sql
   CREATE DATABASE testdb;
   ```
3. 下载MySQL JDBC驱动
4. 修改连接参数后运行

## 学习建议

1. **从SQLite开始** - 不需要安装数据库服务器
2. **理解资源管理** - 学会正确关闭数据库连接
3. **掌握异常处理** - 数据库操作容易出错
4. **练习PreparedStatement** - 这是实际开发中的标准做法
5. **了解事务处理** - 保证数据一致性

## 常见错误

1. **忘记关闭资源** - 导致内存泄漏
2. **SQL注入漏洞** - 使用Statement拼接用户输入
3. **异常处理不当** - 没有正确处理SQLException
4. **连接池误用** - 在生产环境中频繁创建连接

## 进阶主题

- 连接池（Connection Pool）
- 事务管理（Transaction）
- 批处理优化（Batch Processing）
- 存储过程调用（Stored Procedure）
- 数据库元数据（DatabaseMetaData）
