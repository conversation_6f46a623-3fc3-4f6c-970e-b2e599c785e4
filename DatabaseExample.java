import java.sql.*;

public class DatabaseExample {
    // 数据库连接信息
    private static final String URL = "**********************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "password";
    
    public static void main(String[] args) {
        DatabaseExample example = new DatabaseExample();
        
        // 演示 Statement 的使用
        System.out.println("=== Statement 示例 ===");
        example.statementExample();
        
        System.out.println("\n=== PreparedStatement 示例 ===");
        // 演示 PreparedStatement 的使用
        example.preparedStatementExample();
    }
    
    /**
     * Statement 示例
     * 特点：每次执行都需要编译SQL，容易SQL注入，适合静态SQL
     */
    public void statementExample() {
        Connection conn = null;
        Statement stmt = null;
        ResultSet rs = null;
        
        try {
            // 1. 建立数据库连接
            conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            System.out.println("数据库连接成功！");
            
            // 2. 创建Statement对象
            stmt = conn.createStatement();
            
            // 3. 创建表（如果不存在）
            String createTableSQL = "CREATE TABLE IF NOT EXISTS users (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(50) NOT NULL, " +
                    "email VARCHAR(100), " +
                    "age INT)";
            stmt.executeUpdate(createTableSQL);
            System.out.println("表创建成功！");
            
            // 4. 插入数据
            String insertSQL1 = "INSERT INTO users (name, email, age) VALUES ('张三', '<EMAIL>', 25)";
            String insertSQL2 = "INSERT INTO users (name, email, age) VALUES ('李四', '<EMAIL>', 30)";
            
            int rows1 = stmt.executeUpdate(insertSQL1);
            int rows2 = stmt.executeUpdate(insertSQL2);
            System.out.println("插入了 " + (rows1 + rows2) + " 条记录");
            
            // 5. 查询数据
            String selectSQL = "SELECT * FROM users";
            rs = stmt.executeQuery(selectSQL);
            
            System.out.println("查询结果：");
            while (rs.next()) {
                int id = rs.getInt("id");
                String name = rs.getString("name");
                String email = rs.getString("email");
                int age = rs.getInt("age");
                
                System.out.printf("ID: %d, 姓名: %s, 邮箱: %s, 年龄: %d%n", 
                                id, name, email, age);
            }
            
            // 6. 更新数据
            String updateSQL = "UPDATE users SET age = 26 WHERE name = '张三'";
            int updateRows = stmt.executeUpdate(updateSQL);
            System.out.println("更新了 " + updateRows + " 条记录");
            
        } catch (SQLException e) {
            System.err.println("Statement操作失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 7. 关闭资源（重要！）
            closeResources(rs, stmt, conn);
        }
    }
    
    /**
     * PreparedStatement 示例
     * 特点：预编译SQL，防止SQL注入，性能更好，适合动态SQL
     */
    public void preparedStatementExample() {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            // 1. 建立数据库连接
            conn = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            
            // 2. 清空之前的数据（为了演示）
            String deleteSQL = "DELETE FROM users";
            PreparedStatement deletePstmt = conn.prepareStatement(deleteSQL);
            deletePstmt.executeUpdate();
            deletePstmt.close();
            System.out.println("清空表数据");
            
            // 3. 使用PreparedStatement插入数据
            String insertSQL = "INSERT INTO users (name, email, age) VALUES (?, ?, ?)";
            pstmt = conn.prepareStatement(insertSQL);
            
            // 插入多条记录
            String[][] userData = {
                {"王五", "<EMAIL>", "28"},
                {"赵六", "<EMAIL>", "32"},
                {"孙七", "<EMAIL>", "24"}
            };
            
            for (String[] user : userData) {
                pstmt.setString(1, user[0]);  // 设置第1个参数
                pstmt.setString(2, user[1]);  // 设置第2个参数
                pstmt.setInt(3, Integer.parseInt(user[2]));  // 设置第3个参数
                
                int rows = pstmt.executeUpdate();
                System.out.println("插入用户: " + user[0] + ", 影响行数: " + rows);
            }
            
            // 4. 使用PreparedStatement查询数据（带条件）
            String selectSQL = "SELECT * FROM users WHERE age > ? AND name LIKE ?";
            PreparedStatement selectPstmt = conn.prepareStatement(selectSQL);
            selectPstmt.setInt(1, 25);        // 年龄大于25
            selectPstmt.setString(2, "%六%");   // 姓名包含"六"
            
            rs = selectPstmt.executeQuery();
            
            System.out.println("\n条件查询结果（年龄>25且姓名包含'六'）：");
            while (rs.next()) {
                System.out.printf("ID: %d, 姓名: %s, 邮箱: %s, 年龄: %d%n",
                                rs.getInt("id"),
                                rs.getString("name"),
                                rs.getString("email"),
                                rs.getInt("age"));
            }
            
            rs.close();
            selectPstmt.close();
            
            // 5. 使用PreparedStatement更新数据
            String updateSQL = "UPDATE users SET email = ? WHERE name = ?";
            PreparedStatement updatePstmt = conn.prepareStatement(updateSQL);
            updatePstmt.setString(1, "<EMAIL>");
            updatePstmt.setString(2, "王五");
            
            int updateRows = updatePstmt.executeUpdate();
            System.out.println("更新了 " + updateRows + " 条记录");
            updatePstmt.close();
            
            // 6. 批量操作示例
            String batchInsertSQL = "INSERT INTO users (name, email, age) VALUES (?, ?, ?)";
            PreparedStatement batchPstmt = conn.prepareStatement(batchInsertSQL);
            
            // 添加批量数据
            String[][] batchData = {
                {"批量用户1", "<EMAIL>", "20"},
                {"批量用户2", "<EMAIL>", "21"},
                {"批量用户3", "<EMAIL>", "22"}
            };
            
            for (String[] user : batchData) {
                batchPstmt.setString(1, user[0]);
                batchPstmt.setString(2, user[1]);
                batchPstmt.setInt(3, Integer.parseInt(user[2]));
                batchPstmt.addBatch();  // 添加到批处理
            }
            
            int[] batchResults = batchPstmt.executeBatch();  // 执行批处理
            System.out.println("批量插入完成，影响行数: " + batchResults.length);
            batchPstmt.close();
            
            // 7. 最终查询所有数据
            String finalSelectSQL = "SELECT * FROM users ORDER BY id";
            PreparedStatement finalPstmt = conn.prepareStatement(finalSelectSQL);
            rs = finalPstmt.executeQuery();
            
            System.out.println("\n最终所有用户数据：");
            while (rs.next()) {
                System.out.printf("ID: %d, 姓名: %s, 邮箱: %s, 年龄: %d%n",
                                rs.getInt("id"),
                                rs.getString("name"),
                                rs.getString("email"),
                                rs.getInt("age"));
            }
            
            finalPstmt.close();
            
        } catch (SQLException e) {
            System.err.println("PreparedStatement操作失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭资源
            closeResources(rs, pstmt, conn);
        }
    }
    
    /**
     * 关闭数据库资源的工具方法
     */
    private void closeResources(ResultSet rs, Statement stmt, Connection conn) {
        try {
            if (rs != null) rs.close();
            if (stmt != null) stmt.close();
            if (conn != null) conn.close();
            System.out.println("数据库资源已关闭");
        } catch (SQLException e) {
            System.err.println("关闭资源时出错: " + e.getMessage());
        }
    }
}
