import javax.swing.JButton;
import javax.swing.JFrame;
import java.awt.BorderLayout;

public class Main {

    public static void main(String[] args) {
        // 创建一个 JFrame
        JFrame frame = new JFrame("JFrame 默认布局 (BorderLayout)");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(500, 300); // 设置窗口大小

        // 1. 创建几个按钮
        JButton btnNorth = new JButton("North (北)");
        JButton btnSouth = new JButton("South (南)");
        JButton btnWest = new JButton("West (西)");
        JButton btnEast = new JButton("East (东)");
        JButton btnCenter = new JButton("Center (中央)");

        // 2. 将按钮添加到 JFrame 的不同区域
        // 需要使用 add(Component, Object) 的重载方法来指定区域
        frame.add(btnNorth, BorderLayout.NORTH);
        frame.add(btnSouth, BorderLayout.SOUTH);
        frame.add(btnWest, BorderLayout.WEST);
        frame.add(btnEast, BorderLayout.EAST);
        frame.add(btnCenter, BorderLayout.CENTER); // 或者 frame.add(btnCenter); 效果一样

        // 3. 让窗口可见
        frame.setVisible(true);
    }
}