import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

public class Main implements ActionListener {

    JLabel l;
    JTextField t;
    JButton b;

    Main(){
        JFrame a = new JFrame();
        t = new JTextField(20);
        l = new JLabel();
        b = new JButton("click me");
        l.setHorizontalAlignment(JLabel.CENTER);
        a.setLayout(new FlowLayout());
        l.setText("请输入你的名字");
        b.addActionListener(this);  // 修复：应该是按钮添加监听器
        a.add(l);
        a.add(t);
        a.add(b);
        a.setVisible(true);
        a.setSize(400, 400);
        a.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
    }

    public void actionPerformed(ActionEvent e){
        l.setText(t.getText() + ",你好");
    }

    public static void main(String[] args) {
        new Main();
    }
}