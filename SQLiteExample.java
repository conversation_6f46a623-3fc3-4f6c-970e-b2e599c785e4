import java.sql.*;

/**
 * SQLite数据库示例 - 不需要安装数据库服务器
 * 需要下载 sqlite-jdbc 驱动包
 */
public class SQLiteExample {
    private static final String DB_URL = "jdbc:sqlite:test.db";
    
    public static void main(String[] args) {
        SQLiteExample example = new SQLiteExample();
        
        System.out.println("=== SQLite Statement vs PreparedStatement 对比 ===");
        example.demonstrateStatementVsPreparedStatement();
    }
    
    public void demonstrateStatementVsPreparedStatement() {
        // 创建数据库和表
        createDatabase();
        
        // 演示Statement的使用
        System.out.println("\n1. Statement 示例:");
        useStatement();
        
        // 演示PreparedStatement的使用
        System.out.println("\n2. PreparedStatement 示例:");
        usePreparedStatement();
        
        // 演示SQL注入防护
        System.out.println("\n3. SQL注入防护对比:");
        demonstrateSQLInjection();
    }
    
    /**
     * 创建数据库和表
     */
    private void createDatabase() {
        try (Connection conn = DriverManager.getConnection(DB_URL);
             Statement stmt = conn.createStatement()) {
            
            // 创建学生表
            String createTableSQL = """
                CREATE TABLE IF NOT EXISTS students (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    age INTEGER,
                    grade TEXT,
                    score REAL
                )
                """;
            
            stmt.executeUpdate(createTableSQL);
            
            // 清空表数据
            stmt.executeUpdate("DELETE FROM students");
            
            System.out.println("数据库和表创建成功！");
            
        } catch (SQLException e) {
            System.err.println("创建数据库失败: " + e.getMessage());
        }
    }
    
    /**
     * Statement 使用示例
     */
    private void useStatement() {
        try (Connection conn = DriverManager.getConnection(DB_URL);
             Statement stmt = conn.createStatement()) {
            
            // 插入数据 - 需要手动拼接SQL
            String insertSQL1 = "INSERT INTO students (name, age, grade, score) VALUES ('张三', 20, '大二', 85.5)";
            String insertSQL2 = "INSERT INTO students (name, age, grade, score) VALUES ('李四', 21, '大三', 92.0)";
            
            stmt.executeUpdate(insertSQL1);
            stmt.executeUpdate(insertSQL2);
            System.out.println("Statement: 插入数据完成");
            
            // 查询数据
            String selectSQL = "SELECT * FROM students WHERE age >= 20";
            ResultSet rs = stmt.executeQuery(selectSQL);
            
            System.out.println("Statement查询结果:");
            while (rs.next()) {
                System.out.printf("  ID: %d, 姓名: %s, 年龄: %d, 年级: %s, 分数: %.1f%n",
                    rs.getInt("id"), rs.getString("name"), rs.getInt("age"),
                    rs.getString("grade"), rs.getDouble("score"));
            }
            
        } catch (SQLException e) {
            System.err.println("Statement操作失败: " + e.getMessage());
        }
    }
    
    /**
     * PreparedStatement 使用示例
     */
    private void usePreparedStatement() {
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            
            // 插入数据 - 使用参数占位符
            String insertSQL = "INSERT INTO students (name, age, grade, score) VALUES (?, ?, ?, ?)";
            
            try (PreparedStatement pstmt = conn.prepareStatement(insertSQL)) {
                // 插入第一条记录
                pstmt.setString(1, "王五");
                pstmt.setInt(2, 19);
                pstmt.setString(3, "大一");
                pstmt.setDouble(4, 88.5);
                pstmt.executeUpdate();
                
                // 插入第二条记录
                pstmt.setString(1, "赵六");
                pstmt.setInt(2, 22);
                pstmt.setString(3, "大四");
                pstmt.setDouble(4, 95.0);
                pstmt.executeUpdate();
                
                System.out.println("PreparedStatement: 插入数据完成");
            }
            
            // 条件查询
            String selectSQL = "SELECT * FROM students WHERE age BETWEEN ? AND ? ORDER BY score DESC";
            
            try (PreparedStatement pstmt = conn.prepareStatement(selectSQL)) {
                pstmt.setInt(1, 19);  // 最小年龄
                pstmt.setInt(2, 21);  // 最大年龄
                
                ResultSet rs = pstmt.executeQuery();
                
                System.out.println("PreparedStatement查询结果 (年龄19-21，按分数降序):");
                while (rs.next()) {
                    System.out.printf("  ID: %d, 姓名: %s, 年龄: %d, 年级: %s, 分数: %.1f%n",
                        rs.getInt("id"), rs.getString("name"), rs.getInt("age"),
                        rs.getString("grade"), rs.getDouble("score"));
                }
            }
            
            // 批量插入示例
            batchInsertExample(conn);
            
        } catch (SQLException e) {
            System.err.println("PreparedStatement操作失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量插入示例
     */
    private void batchInsertExample(Connection conn) throws SQLException {
        String insertSQL = "INSERT INTO students (name, age, grade, score) VALUES (?, ?, ?, ?)";
        
        try (PreparedStatement pstmt = conn.prepareStatement(insertSQL)) {
            // 准备批量数据
            Object[][] batchData = {
                {"孙七", 20, "大二", 87.5},
                {"周八", 21, "大三", 91.0},
                {"吴九", 19, "大一", 83.5}
            };
            
            // 添加到批处理
            for (Object[] data : batchData) {
                pstmt.setString(1, (String) data[0]);
                pstmt.setInt(2, (Integer) data[1]);
                pstmt.setString(3, (String) data[2]);
                pstmt.setDouble(4, (Double) data[3]);
                pstmt.addBatch();
            }
            
            // 执行批处理
            int[] results = pstmt.executeBatch();
            System.out.println("批量插入完成，影响行数: " + results.length);
        }
    }
    
    /**
     * 演示SQL注入防护
     */
    private void demonstrateSQLInjection() {
        String maliciousInput = "'; DROP TABLE students; --";
        
        System.out.println("恶意输入: " + maliciousInput);
        
        // Statement - 容易受到SQL注入攻击
        System.out.println("\nStatement (不安全):");
        try (Connection conn = DriverManager.getConnection(DB_URL);
             Statement stmt = conn.createStatement()) {
            
            // 这种拼接方式很危险！
            String unsafeSQL = "SELECT * FROM students WHERE name = '" + maliciousInput + "'";
            System.out.println("生成的SQL: " + unsafeSQL);
            
            // 注意：这里可能会执行恶意SQL！
            // stmt.executeQuery(unsafeSQL); // 注释掉以防止真的删除表
            
            System.out.println("如果执行上述SQL，可能会删除整个表！");
            
        } catch (SQLException e) {
            System.err.println("SQL执行错误: " + e.getMessage());
        }
        
        // PreparedStatement - 安全，防止SQL注入
        System.out.println("\nPreparedStatement (安全):");
        try (Connection conn = DriverManager.getConnection(DB_URL)) {
            
            String safeSQL = "SELECT * FROM students WHERE name = ?";
            System.out.println("SQL模板: " + safeSQL);
            
            try (PreparedStatement pstmt = conn.prepareStatement(safeSQL)) {
                pstmt.setString(1, maliciousInput);  // 参数会被安全处理
                
                ResultSet rs = pstmt.executeQuery();
                
                System.out.println("查询结果: ");
                if (!rs.next()) {
                    System.out.println("  没有找到匹配的记录（恶意输入被安全处理）");
                }
            }
            
        } catch (SQLException e) {
            System.err.println("PreparedStatement执行错误: " + e.getMessage());
        }
    }
}
