{"version": "0.2.0", "configurations": [{"type": "java", "name": "StudentSortDemo", "request": "launch", "mainClass": "StudentSortDemo", "projectName": "one_e87df786"}, {"type": "java", "name": "SupermarketSystem", "request": "launch", "mainClass": "SupermarketSystem", "projectName": "one_e87df786"}, {"type": "java", "name": "LoginFrame", "request": "launch", "mainClass": "LoginFrame", "projectName": "one_e87df786"}, {"type": "java", "name": "Calculator", "request": "launch", "mainClass": "Calculator", "projectName": "one_e87df786"}, {"type": "java", "name": "CalculatorApp", "request": "launch", "mainClass": "CalculatorApp", "projectName": "one_e87df786"}, {"type": "java", "name": "GradeManagementSystem", "request": "launch", "mainClass": "GradeManagementSystem", "projectName": "one_e87df786"}, {"type": "java", "name": "DirectoryCopy", "request": "launch", "mainClass": "DirectoryCopy", "projectName": "one_e87df786"}, {"type": "java", "name": "FileCopyComparison", "request": "launch", "mainClass": "FileCopyComparison", "projectName": "one_e87df786"}, {"type": "java", "name": "FileSearch", "request": "launch", "mainClass": "FileSearch", "projectName": "one_e87df786"}, {"type": "java", "name": "DirectoryTraversal", "request": "launch", "mainClass": "DirectoryTraversal", "projectName": "one_e87df786"}, {"type": "java", "name": "Divider", "request": "launch", "mainClass": "Divider", "projectName": "one_e87df786"}, {"type": "java", "name": "StudentManagement", "request": "launch", "mainClass": "StudentManagement", "projectName": "one_e87df786"}, {"type": "java", "name": "one", "request": "launch", "mainClass": "one", "projectName": "one_e87df786"}, {"type": "java", "name": "Main", "request": "launch", "mainClass": "Main", "projectName": "one_e87df786"}, {"type": "java", "name": "PerfectNumber", "request": "launch", "mainClass": "PerfectNumber", "projectName": "Java_84561b2f"}, {"type": "java", "name": "FibonacciSequence", "request": "launch", "mainClass": "FibonacciSequence", "projectName": "Java_84561b2f"}, {"type": "java", "name": "solve", "request": "launch", "mainClass": "solve", "projectName": "Java_84561b2f"}, {"type": "java", "name": "Hello", "request": "launch", "mainClass": "Hello", "projectName": "Java_84561b2f"}, {"type": "java", "name": "HelloWorld07", "request": "launch", "mainClass": "HelloWorld07", "projectName": "Java_84561b2f"}, {"type": "java", "name": "Main", "request": "launch", "mainClass": "Main", "projectName": "Java_84561b2f"}, {"type": "java", "name": "helllo", "request": "launch", "mainClass": "helllo", "projectName": "Java_84561b2f"}, {"type": "java", "name": "Current File", "request": "launch", "mainClass": "${file}"}, {"type": "java", "name": "hello", "request": "launch", "mainClass": "hello", "projectName": "Java_84561b2f"}]}